import React, { useState, useMemo, useEffect } from "react";
import {
  Search,
  Filter,
  Download,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Calendar,
  User,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from "lucide-react";

interface AuditLog {
  id: string;
  timestamp: string;
  user: string;
  actionEventType: string;
  objectItem: string;
  detailsDescription: string;
  ipAddress: string;
  status: "Success" | "Failure";
  department?: string;
  role?: string;
}

const mockAuditLogs: AuditLog[] = [
  {
    id: "1",
    timestamp: "07/26/2024 14:30:00",
    user: "<PERSON>",
    actionEventType: "User Login",
    objectItem: "N/A",
    detailsDescription: "Successful login",
    ipAddress: "*************",
    status: "Success",
    department: "IT",
    role: "Administrator",
  },
  {
    id: "2",
    timestamp: "07/26/2024 14:45:15",
    user: "<PERSON>",
    actionEventType: "Report Generated",
    objectItem: "Report #12345",
    detailsDescription: "Generated sales report for Q2",
    ipAddress: "*************",
    status: "Success",
    department: "Sales",
    role: "Manager",
  },
  {
    id: "3",
    timestamp: "07/26/2024 15:00:20",
    user: "Ethan Harper",
    actionEventType: "User Logout",
    objectItem: "N/A",
    detailsDescription: "User logged out",
    ipAddress: "*************",
    status: "Success",
    department: "IT",
    role: "Administrator",
  },
  {
    id: "4",
    timestamp: "07/26/2024 15:15:30",
    user: "Liam Carter",
    actionEventType: "Data Update",
    objectItem: "Customer #67890",
    detailsDescription: "Changed: Max. Amount from $50 to $60",
    ipAddress: "*************",
    status: "Success",
    department: "Finance",
    role: "Analyst",
  },
  {
    id: "5",
    timestamp: "07/26/2024 15:30:45",
    user: "Sophia Evans",
    actionEventType: "System Error",
    objectItem: "N/A",
    detailsDescription: "Failed to process payment transaction",
    ipAddress: "*************",
    status: "Failure",
    department: "Finance",
    role: "Specialist",
  },
  {
    id: "6",
    timestamp: "07/26/2024 16:00:12",
    user: "Marcus Johnson",
    actionEventType: "File Upload",
    objectItem: "Document #456",
    detailsDescription: "Uploaded compliance document",
    ipAddress: "*************",
    status: "Success",
    department: "Compliance",
    role: "Officer",
  },
  {
    id: "7",
    timestamp: "07/26/2024 16:15:33",
    user: "Emma Wilson",
    actionEventType: "Permission Change",
    objectItem: "User #789",
    detailsDescription: "Updated user permissions for dashboard access",
    ipAddress: "*************",
    status: "Success",
    department: "HR",
    role: "Manager",
  },
  {
    id: "8",
    timestamp: "07/26/2024 16:30:18",
    user: "Alex Rodriguez",
    actionEventType: "Data Export",
    objectItem: "Customer Data",
    detailsDescription: "Exported customer database for backup",
    ipAddress: "*************",
    status: "Success",
    department: "IT",
    role: "Specialist",
  },
];

const AuditTrail: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedActionType, setSelectedActionType] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("");
  const [selectedRole, setSelectedRole] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const actionTypes = [
    ...new Set(mockAuditLogs.map((log) => log.actionEventType)),
  ];
  const departments = [
    ...new Set(mockAuditLogs.map((log) => log.department).filter(Boolean)),
  ];
  const roles = [
    ...new Set(mockAuditLogs.map((log) => log.role).filter(Boolean)),
  ];
  const statuses = ["Success", "Failure"];

  const filteredLogs = useMemo(() => {
    return mockAuditLogs.filter((log) => {
      const matchesSearch =
        searchTerm === "" ||
        log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.actionEventType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.detailsDescription
          .toLowerCase()
          .includes(searchTerm.toLowerCase()) ||
        log.objectItem.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesActionType =
        selectedActionType === "" || log.actionEventType === selectedActionType;
      const matchesDepartment =
        selectedDepartment === "" || log.department === selectedDepartment;
      const matchesRole = selectedRole === "" || log.role === selectedRole;
      const matchesStatus =
        selectedStatus === "" || log.status === selectedStatus;

      return (
        matchesSearch &&
        matchesActionType &&
        matchesDepartment &&
        matchesRole &&
        matchesStatus
      );
    });
  }, [
    searchTerm,
    selectedActionType,
    selectedDepartment,
    selectedRole,
    selectedStatus,
  ]);

  const paginatedLogs = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredLogs.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredLogs, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  const handleExport = () => {
    const csvContent = [
      [
        "Timestamp",
        "User",
        "Action/Event Type",
        "Object/Item",
        "Details/Description",
        "IP Address",
        "Status",
        "Department",
        "Role",
      ],
      ...filteredLogs.map((log) => [
        log.timestamp,
        log.user,
        log.actionEventType,
        log.objectItem,
        log.detailsDescription,
        log.ipAddress,
        log.status,
        log.department || "",
        log.role || "",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `audit-trail-${new Date().toISOString().split("T")[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedActionType("");
    setSelectedDepartment("");
    setSelectedRole("");
    setSelectedStatus("");
    setCurrentPage(1);
  };

  const StatusBadge: React.FC<{ status: "Success" | "Failure" }> = ({
    status,
  }) => (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
        status === "Success"
          ? "bg-green-100 text-green-800 border border-green-200"
          : "bg-red-100 text-red-800 border border-red-200"
      }`}
    >
      {status === "Success" ? (
        <CheckCircle className="w-3 h-3 mr-1" />
      ) : (
        <XCircle className="w-3 h-3 mr-1" />
      )}
      {status}
    </span>
  );

  const FilterDropdown: React.FC<{
    label: string;
    value: string;
    onChange: (value: string) => void;
    options: string[];
    icon: React.ReactNode;
  }> = ({ label, value, onChange, options, icon }) => (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">
        {label}
      </label>
      <div className="relative">
        <select
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="appearance-none w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-sm transition-all duration-200 hover:border-gray-400"
        >
          <option value="">All {label}s</option>
          {options.map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none text-gray-400">
          {icon}
        </div>
        <ChevronDown className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none w-5 h-5 text-gray-400" />
      </div>
    </div>
  );

  useEffect(() => {
    setCurrentPage(1);
  }, [
    searchTerm,
    selectedActionType,
    selectedDepartment,
    selectedRole,
    selectedStatus,
  ]);

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <span>Dashboard</span>
            <ChevronRight className="w-4 h-4 mx-2" />
            <span className="text-gray-900 font-medium">Audit Trail</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Audit Trail</h1>
        </div>

        {/* Controls */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-6 transition-shadow duration-300 hover:shadow-md">
          <div className="p-6">
            {/* Search and Actions */}
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Search audit logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                    showFilters
                      ? "bg-blue-500 text-white shadow-md"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  <Filter className="w-4 h-4" />
                  Filters
                </button>
                <button
                  onClick={handleRefresh}
                  disabled={isLoading}
                  className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 font-medium transition-all duration-200 flex items-center gap-2 disabled:opacity-50"
                >
                  <RefreshCw
                    className={`w-4 h-4 ${isLoading ? "animate-spin" : ""}`}
                  />
                  Refresh
                </button>
                <button
                  onClick={handleExport}
                  className="px-4 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 font-medium transition-all duration-200 flex items-center gap-2"
                >
                  <Download className="w-4 h-4" />
                  Export
                </button>
              </div>
            </div>

            {/* Filters */}
            <div
              className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4 transition-all duration-300 overflow-hidden ${
                showFilters
                  ? "max-h-40 opacity-100 mb-6"
                  : "max-h-0 opacity-0 mb-0"
              }`}
            >
              <FilterDropdown
                label="Action Type"
                value={selectedActionType}
                onChange={setSelectedActionType}
                options={actionTypes}
                icon={<Calendar className="w-4 h-4" />}
              />
              <FilterDropdown
                label="Department"
                value={selectedDepartment}
                onChange={setSelectedDepartment}
                options={departments}
                icon={<User className="w-4 h-4" />}
              />
              <FilterDropdown
                label="Role"
                value={selectedRole}
                onChange={setSelectedRole}
                options={roles}
                icon={<Shield className="w-4 h-4" />}
              />
              <FilterDropdown
                label="Status"
                value={selectedStatus}
                onChange={setSelectedStatus}
                options={statuses}
                icon={<AlertTriangle className="w-4 h-4" />}
              />
              <div className="flex items-end">
                <button
                  onClick={clearFilters}
                  className="w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 font-medium transition-all duration-200"
                >
                  Clear Filters
                </button>
              </div>
            </div>

            {/* Results Summary */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 text-sm text-gray-600">
              <div>
                Showing {paginatedLogs.length} of {filteredLogs.length} results
              </div>
              <div className="flex items-center gap-2">
                <label htmlFor="itemsPerPage" className="whitespace-nowrap">
                  Items per page:
                </label>
                <select
                  id="itemsPerPage"
                  value={itemsPerPage}
                  onChange={(e) => {
                    setItemsPerPage(Number(e.target.value));
                    setCurrentPage(1);
                  }}
                  className="border border-gray-300 rounded px-2 py-1 text-sm"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Action/Event Type
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Object/Item
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Details/Description
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {paginatedLogs.map((log, index) => (
                  <tr
                    key={log.id}
                    className="hover:bg-gray-50 transition-colors duration-150"
                    style={{
                      animation: `fadeInUp 0.5s ease-out ${index * 0.05}s both`,
                    }}
                  >
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.timestamp}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {log.user}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {log.actionEventType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {log.objectItem}
                    </td>
                    <td
                      className="px-6 py-4 text-sm text-gray-700 max-w-xs truncate"
                      title={log.detailsDescription}
                    >
                      {log.detailsDescription}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 font-mono">
                      {log.ipAddress}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <StatusBadge status={log.status} />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden">
            {paginatedLogs.map((log, index) => (
              <div
                key={log.id}
                className="border-b border-gray-200 p-6 hover:bg-gray-50 transition-colors duration-150"
                style={{
                  animation: `fadeInUp 0.5s ease-out ${index * 0.05}s both`,
                }}
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <h3 className="text-sm font-semibold text-gray-900 mb-1">
                      {log.user}
                    </h3>
                    <p className="text-xs text-gray-500">{log.timestamp}</p>
                  </div>
                  <StatusBadge status={log.status} />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-500">Action:</span>
                    <span className="text-xs text-gray-900">
                      {log.actionEventType}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-500">Object:</span>
                    <span className="text-xs text-gray-900">
                      {log.objectItem}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-xs text-gray-500">IP:</span>
                    <span className="text-xs text-gray-900 font-mono">
                      {log.ipAddress}
                    </span>
                  </div>
                  <div className="mt-2">
                    <p className="text-xs text-gray-700">
                      {log.detailsDescription}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Empty State */}
          {filteredLogs.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No audit logs found
              </h3>
              <p className="text-gray-500 mb-4">
                Try adjusting your search criteria or filters.
              </p>
              <button
                onClick={clearFilters}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6 flex flex-col sm:flex-row items-center justify-between gap-4">
            <div className="text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="p-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>

              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNumber;
                if (totalPages <= 5) {
                  pageNumber = i + 1;
                } else if (currentPage <= 3) {
                  pageNumber = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNumber = totalPages - 4 + i;
                } else {
                  pageNumber = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNumber}
                    onClick={() => setCurrentPage(pageNumber)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                      currentPage === pageNumber
                        ? "bg-blue-500 text-white"
                        : "border border-gray-300 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    {pageNumber}
                  </button>
                );
              })}

              <button
                onClick={() =>
                  setCurrentPage(Math.min(totalPages, currentPage + 1))
                }
                disabled={currentPage === totalPages}
                className="p-2 border border-gray-300 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 transition-colors duration-200"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      <style>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
};

export default AuditTrail;