import React, { useState } from 'react';
import { Search, Plus } from 'lucide-react';
import PolicyFormView from './PolicyFormView';

type Policy = {
  id: number;
  name: string;
  description: string;
  applicableTo: string;
  lastModified: string;
};

type FormValues = {
  policyName: string;
  description: string;
  maxFrequency: string;
  receiptRequirement: string;
  expenseCategories: string;
  maxAmount: number;
  perDiem: number;
  destinationTypes: string;
  bookingClass: string;
  advanceBookingReq: string;
  applicableDepartments: string;
  applicableRoles: string;
};

const PolicyListView: React.FC = () => {
  const [currentView, setCurrentView] = useState<'list' | 'form'>('list');
  const [editingPolicy, setEditingPolicy] = useState<Partial<FormValues> | null>(null);

  const [policies] = useState<Policy[]>([
    {
      id: 1,
      name: 'Travel Expenses',
      description: 'Covers travel costs for business trips, including transportation, accommodation, and meals.',
      applicableTo: 'All Employees',
      lastModified: '2023-11-15',
    },
    {
      id: 2,
      name: 'Meal Expenses',
      description: 'Reimbursement for meals during business travel or client meetings.',
      applicableTo: 'Sales Team',
      lastModified: '2023-10-20',
    },
    {
      id: 3,
      name: 'Office Supplies',
      description: 'Covers the purchase of necessary office supplies.',
      applicableTo: 'Administrative Staff',
      lastModified: '2023-09-05',
    },
    {
      id: 4,
      name: 'Training and Development',
      description: 'Reimbursement for professional development courses and training programs.',
      applicableTo: 'All Employees',
      lastModified: '2023-08-12',
    },
    {
      id: 5,
      name: 'Client Entertainment',
      description: 'Expenses for entertaining clients, including meals and activities.',
      applicableTo: 'Sales Team',
      lastModified: '2023-07-28',
    },
  ]);

  const handleBackToList = () => {
    setCurrentView('list');
    setEditingPolicy(null);
  };

  const handleEditPolicy = (policy: Policy) => {
    setEditingPolicy({
      policyName: policy.name,
      description: policy.description,
      applicableDepartments: '',
      applicableRoles: '',
      maxFrequency: '',
      receiptRequirement: '',
      expenseCategories: '',
      maxAmount: 0,
      perDiem: 0,
      destinationTypes: '',
      bookingClass: '',
      advanceBookingReq: '',
    });
    setCurrentView('form');
  };

  const handleAddPolicy = () => {
    setEditingPolicy(null);
    setCurrentView('form');
  };

  return (
    <div className="flex min-h-screen">
      <div className="flex-1 bg-slate-50 p-6 overflow-y-auto">
        {currentView === 'list' ? (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
              <div>
                <h1 className="text-2xl font-medium text-slate-600 mb-2">Policy Management</h1>
                <div className="flex items-center text-sm text-slate-400">
                  <span>Dashboard</span>
                  <span className="mx-2">/</span>
                  <span className="text-teal-600">Policy Management</span>
                </div>
              </div>
              <button
                onClick={handleAddPolicy}
                className="brand-gradient text-white px-6 py-2.5 rounded-md hover:bg-slate-800 flex items-center gap-2 font-medium text-sm"
              >
                <Plus size={16} />
                Add Policy
              </button>
            </div>

            {/* Search */}
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400" size={18} />
              <input
                type="text"
                placeholder="Search policies"
                className="w-full pl-12 pr-4 py-3 border-0 rounded-md bg-slate-100 text-slate-600 placeholder-slate-400 focus:outline-none focus:ring-0 focus:bg-white focus:shadow-sm"
              />
            </div>

            {/* Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden border border-slate-200">
              <table className="w-full">
                <thead className="bg-slate-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-medium text-slate-600">Policy Name</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-slate-600">Description</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-slate-600">Applicable To</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-slate-600">Last Modified</th>
                    <th className="px-6 py-4 text-left text-sm font-medium text-slate-600">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-100">
                  {policies.map((policy) => (
                    <tr key={policy.id} className="hover:bg-slate-25">
                      <td className="px-6 py-4 text-teal-600 font-medium text-sm">{policy.name}</td>
                      <td className="px-6 py-4 text-slate-600 text-sm max-w-md">{policy.description}</td>
                      <td className="px-6 py-4 text-slate-600 text-sm">{policy.applicableTo}</td>
                      <td className="px-6 py-4 text-slate-600 text-sm">{policy.lastModified}</td>
                      <td className="px-6 py-4">
                        <button
                          onClick={() => handleEditPolicy(policy)}
                          className="text-teal-600 hover:text-teal-700 text-sm font-medium"
                        >
                          View/Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <PolicyFormView editingPolicy={editingPolicy} handleBackToList={handleBackToList} />
        )}
      </div>
    </div>
  );
};

export default PolicyListView;
