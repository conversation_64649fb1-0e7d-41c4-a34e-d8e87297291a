import { useState } from 'react';
import { <PERSON><PERSON>ilter, FiCalendar } from 'react-icons/fi';
import { Button } from '../../../components/Button';
import type { ExpenseData, ApprovalData, DisputeData } from '../../../mockData/mockData';
import Pagination from '../../../components/Pagination';
import Dropdown from '../../../components/Dropdown';

interface TabContentProps {
  activeTab: string;
  tabCategory: string;
  expenseData?: ExpenseData[];
  approvalData?: ApprovalData[];
  disputeData?: DisputeData[];
  getStatusBadgeClass: (status: string) => string;
}

const ITEMS_PER_PAGE = 5;

const TabContent = ({
  activeTab,
  tabCategory,
  expenseData,
  approvalData,
  disputeData,
  getStatusBadgeClass,
}: TabContentProps) => {
  const [currentPage, setCurrentPage] = useState(1);

  const getTabTitle = () => {
    switch (tabCategory) {
      case 'all':
        return 'All Expenses';
      case 'approvals':
        return 'Management Approvals';
      case 'disputes':
        return 'Raised Disputes';
      default:
        return 'All Expenses';
    }
  };

  const getCurrentData = () => {
    switch (tabCategory) {
      case 'all':
        return expenseData || [];
      case 'approvals':
        return approvalData || [];
      case 'disputes':
        return disputeData || [];
      default:
        return expenseData || [];
    }
  };

  const currentData = getCurrentData();
  const totalPages = Math.ceil(currentData.length / ITEMS_PER_PAGE);
  const paginatedData = currentData.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  return (
    <div className={`${activeTab === tabCategory ? 'block' : 'hidden'}`}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 sm:p-6">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">{getTabTitle()}</h2>
            <Button className="border border-gray-300 text-gray-700 hover:bg-gray-50 px-4 py-2 w-full sm:w-auto">
              <FiFilter className="w-4 h-4" />
              Filters
            </Button>
          </div>

          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            {tabCategory === 'all' && (
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Employee Name</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Submission Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Total Amount</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Approval Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Payment Method</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {paginatedData.length > 0 ? (
                    paginatedData.map((expense) => {
                      const expenseItem = expense as ExpenseData;
                      return (
                        <tr key={expenseItem.id} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-4 px-4 text-sm text-gray-900">{expenseItem.employeeName}</td>
                          <td className="py-4 px-4 text-sm text-gray-600">{expenseItem.department}</td>
                          <td className="py-4 px-4 text-sm text-gray-600">{expenseItem.submissionDate}</td>
                          <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                            ${expenseItem.totalAmount.toFixed(2)}
                          </td>
                          <td className="py-4 px-4">
                            <span className={getStatusBadgeClass(expenseItem.approvalStatus)}>
                              {expenseItem.approvalStatus}
                            </span>
                          </td>
                          <td className="py-4 px-4 text-sm text-gray-600">{expenseItem.paymentMethod}</td>
                          <td className="py-4 px-4">
                            <button className="text-sm text-gray-500 hover:text-gray-700">
                              Mark Action
                            </button>
                          </td>
                        </tr>
                      );
                    })
                  ) : (
                    <tr>
                      <td colSpan={7} className="py-8 px-4 text-center text-gray-500">
                        No expenses found for this category
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            )}

            {tabCategory === 'approvals' && (
              <div>
                {/* Filter Section for Approvals */}
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Date Range</span>
                  </div>
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager', 'Senior Manager', 'Team Lead']}
                    label="Designation"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Marketing', 'Sales', 'Engineering', 'Product Design']}
                    label="Department"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Pending', 'Approved', 'Rejected']}
                    label="Status"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager Approved', 'Finance Approved', 'Pending']}
                    label="Approval Status"
                    className="min-w-[140px]"
                  />
                </div>

                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Employee Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Designation</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense Category</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Submission Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Total Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.length > 0 ? (
                      paginatedData.map((approval) => {
                        const approvalItem = approval as ApprovalData;
                        return (
                          <tr key={approvalItem.id} className="border-b border-gray-100 hover:bg-gray-50">
                            <td className="py-4 px-4 text-sm text-gray-900">{approvalItem.employeeName}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.department}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.designation}</td>
                            <td className="py-4 px-4 text-sm text-gray-600 flex items-center gap-2">
                              <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                                <img src='categoryIcon.svg' alt="Category Icon" />
                              </span>
                              {approvalItem.expenseCategory}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{approvalItem.submissionDate}</td>
                            <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                              ${approvalItem.totalAmount.toFixed(2)}
                            </td>
                            <td className="py-4 px-4">
                              <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm hover:bg-blue-200">
                                View
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={7} className="py-8 px-4 text-center text-gray-500">
                          No approvals found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {tabCategory === 'disputes' && (
              <div>
                {/* Filter Section for Disputes */}
                <div className="flex flex-wrap gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <FiCalendar className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Date Range</span>
                  </div>
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Multiple Expenses', 'Conference Expenses', 'Software Licenses', 'Travel Expenses']}
                    label="Expense Type"
                    className="min-w-[140px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Marketing', 'Sales', 'Engineering', 'Development', 'HR']}
                    label="Department"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Pending', 'Approved', 'In review']}
                    label="Status"
                    className="min-w-[120px]"
                  />
                  <Dropdown
                    value=""
                    onChange={() => {}}
                    options={['Manager', 'Finance']}
                    label="Approval Status"
                    className="min-w-[140px]"
                  />
                </div>

                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense type</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Raised On</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Rejected By</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Current Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.length > 0 ? (
                      paginatedData.map((dispute) => {
                        const disputeItem = dispute as DisputeData;
                        return (
                          <tr key={disputeItem.id} className="border-b border-gray-100 hover:bg-gray-50">
                            <td className="py-4 px-4 text-sm text-gray-900">{disputeItem.name}</td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.department}</td>
                            <td className="py-4 px-4 text-sm text-gray-600 flex items-center gap-2">
                              <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                                <img src='categoryIcon.svg' alt="Category Icon" />
                              </span>
                              {disputeItem.expenseType}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.disputeRaisedOn}</td>
                            <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                              ${disputeItem.disputeAmount.toFixed(2)}
                            </td>
                            <td className="py-4 px-4 text-sm text-gray-600">{disputeItem.rejectedBy}</td>
                            <td className="py-4 px-4">
                              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                                disputeItem.currentStatus === 'Approved'
                                  ? 'bg-green-100 text-green-800'
                                  : disputeItem.currentStatus === 'Pending'
                                  ? 'bg-gray-100 text-gray-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {disputeItem.currentStatus}
                              </span>
                            </td>
                            <td className="py-4 px-4">
                              <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded text-sm hover:bg-blue-200">
                                View Details
                              </button>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="py-8 px-4 text-center text-gray-500">
                          No disputes found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Mobile Cards */}
          <div className="lg:hidden space-y-4">
            {tabCategory === 'all' && paginatedData.length > 0 ? (
              paginatedData.map((expense) => {
                const expenseItem = expense as ExpenseData;
                return (
                  <div key={expenseItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{expenseItem.employeeName}</h3>
                        <p className="text-xs text-gray-500">{expenseItem.department}</p>
                      </div>
                      <span className={getStatusBadgeClass(expenseItem.approvalStatus)}>
                        {expenseItem.approvalStatus}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Date:</span>
                        <p className="text-gray-900">{expenseItem.submissionDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${expenseItem.totalAmount.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Payment:</span>
                        <p className="text-gray-900">{expenseItem.paymentMethod}</p>
                      </div>
                      <div className="flex justify-end">
                        <button className="text-xs text-gray-500 hover:text-gray-700 bg-white px-3 py-1 rounded border">
                          Mark Action
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : tabCategory === 'approvals' && paginatedData.length > 0 ? (
              paginatedData.map((approval) => {
                const approvalItem = approval as ApprovalData;
                return (
                  <div key={approvalItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{approvalItem.employeeName}</h3>
                        <p className="text-xs text-gray-500">{approvalItem.department} • {approvalItem.designation}</p>
                      </div>
                      <button className="bg-blue-100 text-blue-700 px-3 py-1 rounded text-xs">
                        View
                      </button>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Category:</span>
                        <p className="text-gray-900">{approvalItem.expenseCategory}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Date:</span>
                        <p className="text-gray-900">{approvalItem.submissionDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${approvalItem.totalAmount.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : tabCategory === 'disputes' && paginatedData.length > 0 ? (
              paginatedData.map((dispute) => {
                const disputeItem = dispute as DisputeData;
                return (
                  <div key={disputeItem.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900 text-sm">{disputeItem.name}</h3>
                        <p className="text-xs text-gray-500">{disputeItem.department}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        disputeItem.currentStatus === 'Approved'
                          ? 'bg-green-100 text-green-800'
                          : disputeItem.currentStatus === 'Pending'
                          ? 'bg-gray-100 text-gray-800'
                          : 'bg-blue-100 text-blue-800'
                      }`}>
                        {disputeItem.currentStatus}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div>
                        <span className="text-gray-500">Type:</span>
                        <p className="text-gray-900">{disputeItem.expenseType}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Raised On:</span>
                        <p className="text-gray-900">{disputeItem.disputeRaisedOn}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Amount:</span>
                        <p className="text-gray-900 font-medium">${disputeItem.disputeAmount.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Rejected By:</span>
                        <p className="text-gray-900">{disputeItem.rejectedBy}</p>
                      </div>
                      <div className="col-span-2 flex justify-end">
                        <button className="text-xs text-blue-700 hover:text-blue-800 bg-blue-100 px-3 py-1 rounded">
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="py-8 text-center text-gray-500">
                No data found for this category
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={(page) => setCurrentPage(page)}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TabContent;
