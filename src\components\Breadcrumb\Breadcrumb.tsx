import { useLocation, useNavigate } from 'react-router-dom';

interface BreadcrumbProps {
  pageName: string;
}

const Breadcrumb = ({ pageName }: BreadcrumbProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  if (location.pathname === '/login') {
    return null;
  }

  const handleDashboardClick = () => {
    navigate('/');
  };

  return (
    <nav className="text-sm text-gray-500 mb-2 font-semibold">
      <span 
        className="cursor-pointer hover:text-teal-600 transition-colors"
        onClick={handleDashboardClick}
      >
        Dashboard
      </span>
      <span className="mx-2">/</span>
      <span className="text-teal-600">
        {pageName}
      </span>
    </nav>
  );
};

export default Breadcrumb;