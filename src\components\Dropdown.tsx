import { ChevronDown } from "lucide-react";

interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  label: string;
  className?: string;
}

export default function Dropdown({
  value,
  onChange,
  options,
  label,
  className = "",
}: DropdownProps) {
  return (
    <div className={`relative w-fit ${className}`}>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="appearance-none bg-gray-50 hover:bg-gray-100 text-sm text-gray-700 px-3 py-2 pr-8 rounded-xl shadow-sm outline-none transition w-full"
      >
        <option value="">{label}</option>
        {options.map((opt) => (
          <option key={opt} value={opt}>
            {opt}
          </option>
        ))}
      </select>
      <ChevronDown className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-500 pointer-events-none" />
    </div>
  );
}
