import React from "react";

interface Props {
  status: "Connected" | "Disconnected";
}
const StatusBadge: React.FC<Props> = ({ status }) => {
  const color =
    status === "Connected"
      ? "bg-green-100 text-green-700"
      : "bg-red-100 text-red-700";
  return (
    <span className={`px-3 py-1 rounded-full text-xs font-semibold ${color}`}>
      {status}
    </span>
  );
};
export default StatusBadge;