import { Link } from 'react-router-dom';
import AuthPageLayout from '../../components/AuthLayout';

const Signup = () => {
    return (
        <AuthPageLayout>
            <section className="grid grid-cols-2 gap-x-20 pt-32 relative">
                {/* Left Side - Branding */}
                <section className="h-64 flex items-center justify-center">
                    <div className="max-w-xl rounded text-white">
                        {/* Logo Block */}
                        <div className="flex items-center mb-4">
                            {/* Logo Icon */}
                            <div className="mr-3 w-20 h-20">
                                <img
                                    src="/expenso-logo.png"
                                    alt="Expenso Logo"
                                    className="w-full h-full object-contain"
                                />
                            </div>

                            {/* Logo Text */}
                            <div className="w-70 h-10">
                                <img
                                    src="/expenso-text.svg"
                                    alt="EXPENSO"
                                    className="w-full h-full object-contain"
                                />
                            </div>
                        </div>

                        {/* Subtitle */}
                        <p className="text-xs font-medium uppercase tracking-[1.4em] mb-6">
                            Admin Dashboard
                        </p>

                        {/* Section Content */}
                        <h1 className="font-semibold mb-2 text-4xl">Sign in to</h1>
                        <p className="text-lg font-light my-4">
                            Streamline your company’s expense management <br />
                            in one secure platform.
                        </p>
                        <p className="text-sm font-light">
                            Sign in to get started and take control of your <br />
                            expenses today.
                        </p>
                    </div>
                </section>

                {/* Right Side - Form */}
                <section className="z-20 mx-auto bg-white rounded-2xl p-12 shadow-lg absolute right-65 top-4/10">
                    <div className="flex justify-between mb-2">
                        <h3 className="text-xl font-normal">
                            Welcome to <span className="text-[#0ee6c9] font-semibold">EXPENSO</span>
                        </h3>
                        <p className="text-sm text-gray-500">
                            Have an Account? <br />
                            <Link to="/login" className="text-[#0ee6c9] font-medium hover:underline">
                                Sign in
                            </Link>
                        </p>
                    </div>

                    <h1 className="text-6xl font-semibold mb-12">Sign up</h1>

                    <form className="grid gap-8 w-110" onSubmit={(e) => e.preventDefault()}>
                        {/* Email */}
                        <div className="grid gap-2">
                            <label htmlFor="email" className="text-sm font-normal text-gray-800">
                                Enter your username or email address
                            </label>
                            <input
                                id="email"
                                type="email"
                                placeholder="Username or email address"
                                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                                required
                            />
                        </div>

                        {/* Username + Contact */}
                        <div className="grid grid-cols-2 gap-4">
                            <div className="grid gap-2">
                                <label htmlFor="username" className="text-sm font-normal text-gray-800">
                                    User name
                                </label>
                                <input
                                    id="username"
                                    type="text"
                                    placeholder="User name"
                                    className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                                    required
                                />
                            </div>

                            <div className="grid gap-2">
                                <label htmlFor="contact" className="text-sm font-normal text-gray-800">
                                    Contact Number
                                </label>
                                <input
                                    id="contact"
                                    type="tel"
                                    placeholder="Contact Number"
                                    className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                                    required
                                />
                            </div>
                        </div>

                        {/* Password */}
                        <div className="grid gap-2">
                            <label htmlFor="password" className="text-sm font-normal text-gray-700">
                                Enter your Password
                            </label>
                            <input
                                id="password"
                                type="password"
                                placeholder="Password"
                                className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
                                required
                            />
                        </div>

                        {/* Submit Button */}
                        <button
                            type="submit"
                            className="brand-gradient mt-10 rounded-lg text-white py-3 font-semibold hover:opacity-90 transition"
                        >
                            Sign up
                        </button>
                    </form>
                </section>
            </section>
        </AuthPageLayout>
    );
};

export default Signup;
