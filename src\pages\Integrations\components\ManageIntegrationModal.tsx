import React from "react";
import { useForm, Controller } from "react-hook-form";
import { AnimatePresence, motion } from "framer-motion";

type Status = "Connected" | "Disconnected";
type Integration = {
    name: string;
    description: string;
    status: Status;
    account?: string;
    lastConnected?: string;
};

type Props = {
    open: boolean;
    onClose: () => void;
    integration?: Integration;
};

const SYNC_OPTIONS = ["Hourly", "Daily", "Manual"];

export default function ManageIntegrationModal({
    open,
    onClose,
    integration,
}: Props) {
    const { register, handleSubmit, control, reset } = useForm({
        defaultValues: {
            autoSync: false,
            syncCycle: "Daily",
            data: "",
        },
    });

    React.useEffect(() => {
        if (open && integration) {
            reset({
                autoSync: false,
                syncCycle: "Daily",
                data: "",
            });
        }
    }, [open, integration, reset]);

    if (!open || !integration) return null;

    return (
        <AnimatePresence>
            <motion.div
                className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
            >
                <motion.form
                    onClick={(e) => e.stopPropagation()}
                    onSubmit={handleSubmit(() => {
                        onClose();
                    })}
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.95, opacity: 0 }}
                    className="flex flex-col w-full max-w-3xl bg-white rounded-3xl shadow-lg px-8 py-8 md:px-14 md:py-10 relative"
                    style={{ maxHeight: "90vh", overflowY: "auto" }}
                >
                    <button
                        type="button"
                        onClick={onClose}
                        className="absolute top-7 right-7 text-[#ff3d46] text-md font-semibold outline-none focus:ring"
                    >
                        Close
                    </button>
                    <div className="flex flex-col md:flex-row gap-8">
                        {/* Left Column */}
                        <div className="flex-1 min-w-[170px]">
                            <div className="text-xl font-bold text-[#223557] mb-5">
                                Manage Integration
                            </div>
                            <div className="mb-6">
                                <div className="font-semibold text-sm text-[#223557] mb-2">
                                    Integration Status
                                </div>
                                <div className="flex items-center gap-2">
                                    <span className="inline-flex items-center justify-center w-6 h-6 rounded bg-[#cbfadf]">
                                        <span className="material-symbols-outlined text-green-500">
                                            check
                                        </span>
                                    </span>
                                    <span className="font-medium text-[#2b8773] text-sm">
                                        Connected
                                    </span>
                                </div>
                            </div>
                            <div>
                                <div className="font-semibold text-sm text-[#223557] mb-2">
                                    Connection Details
                                </div>
                                <div className="mb-1">
                                    <span className="block text-xs text-gray-600 font-semibold">
                                        Connected Account
                                    </span>
                                    <span className="text-gray-900 text-sm">
                                        {integration.account || "—"}
                                    </span>
                                </div>
                                <div className="mb-3">
                                    <span className="block text-xs text-gray-600 font-semibold">
                                        Last Connected
                                    </span>
                                    <span className="text-gray-900 text-sm">
                                        {integration.lastConnected || "—"}
                                    </span>
                                </div>
                                <button
                                    type="button"
                                    className="px-5 py-2 bg-gray-100 rounded-full font-semibold shadow focus:outline-none hover:bg-gray-200 text-sm"
                                >
                                    Reconnect
                                </button>
                            </div>
                        </div>
                        {/* Right Column */}
                        <div className="flex-1 min-w-[240px]">
                            <div className="font-semibold text-sm text-[#223557] mb-2">
                                Synchronization Settings
                            </div>
                            <div className="flex items-center gap-2 mb-3">
                                <span className="text-gray-700 text-sm mr-2">
                                    Enable Automatic Sync
                                </span>
                                <Controller
                                    name="autoSync"
                                    control={control}
                                    render={({ field }) => (
                                        <label className="inline-flex items-center cursor-pointer">
                                            <input
                                                type="checkbox"
                                                className="sr-only peer"
                                                checked={field.value}
                                                onChange={field.onChange}
                                            />
                                            <div className="w-10 h-6 bg-gray-200 rounded-full peer peer-checked:bg-[#cbfadf] transition-colors duration-200 relative">
                                                <span
                                                    className="absolute left-1 top-1 bg-white w-4 h-4 rounded-full shadow transition-all peer-checked:left-5"
                                                    style={{
                                                        boxShadow: "0 1px 4px 0 #0001",
                                                    }}
                                                />
                                            </div>
                                        </label>
                                    )}
                                />

                            </div>
                            <div className="mb-4 flex flex-wrap gap-3">
                                {SYNC_OPTIONS.map((cycle) => (
                                    <label key={cycle} className="inline-block">
                                        <input
                                            type="radio"
                                            value={cycle}
                                            {...register("syncCycle")}
                                            className="sr-only"
                                        />
                                        <span className="inline-block px-4 py-2 border border-gray-200 rounded-xl font-medium text-gray-700 text-sm cursor-pointer hover:bg-gray-100">
                                            {cycle}
                                        </span>
                                    </label>
                                ))}
                            </div>
                            <div className="font-semibold text-sm text-[#223557] mb-2">
                                Data Mapping
                            </div>
                            <select
                                {...register("data")}
                                aria-label="Select Data to Sync"
                                className="w-full border border-gray-200 text-gray-700 rounded-xl px-3 py-2 mb-5 bg-white text-sm focus:outline-[#1976d2]"
                                defaultValue=""
                            >
                                <option value="">Choose data</option>
                                <option value="all">All Data</option>
                                <option value="customers">Customers</option>
                                <option value="transactions">Transactions</option>
                            </select>
                            <div className="flex gap-2 mb-8">
                                <button
                                    type="button"
                                    className="px-4 py-2 font-semibold bg-gray-100 rounded-full text-sm transition hover:bg-gray-200"
                                >
                                    Test Connection
                                </button>
                                <button
                                    type="button"
                                    className="px-4 py-2 font-semibold bg-gray-100 rounded-full text-sm transition hover:bg-gray-200"
                                >
                                    View Logs
                                </button>
                            </div>
                        </div>
                    </div>
                    <div className="flex w-full justify-end mt-2">
                        <button
                            type="submit"
                            className="inline-block rounded-xl py-3 px-8 text-white text-lg font-semibold bg-gradient-to-r from-[#043652] to-[#214060] shadow focus:outline-none transition hover:brightness-110"
                        >
                            Save Changes
                        </button>
                    </div>
                </motion.form>
                {/* click-to-close backdrop */}
                <div className="fixed inset-0 z-40" onClick={onClose} />
            </motion.div>
        </AnimatePresence>
    );
}