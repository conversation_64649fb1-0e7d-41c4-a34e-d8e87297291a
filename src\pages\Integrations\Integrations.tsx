import { useState } from "react";
import ManageIntegrationModal from "./components/ManageIntegrationModal";

type Status = "Connected" | "Disconnected";
type Integration = {
  name: string;
  description: string;
  status: Status;
  account?: string;
  lastConnected?: string;
};

const INTEGRATIONS: Integration[] = [
  {
    name: "Accounting System",
    description:
      "Connect your accounting system to automate financial data synchronization.",
    status: "Connected",
    account: "Tech Solutions Inc.",
    lastConnected: "July 20, 2025, 10:30 AM",
  },
  {
    name: "HR Platform",
    description:
      "Integrate your HR platform to streamline employee data management.",
    status: "Disconnected",
  },
  {
    name: "Payment Gateway",
    description:
      "Enable payment processing through your preferred payment gateway.",
    status: "Connected",
    account: "Tech Solutions Inc.",
    lastConnected: "July 20, 2025, 10:30 AM",
  },
  {
    name: "CRM Tool",
    description: "Sync customer data between your CRM and our platform.",
    status: "Disconnected",
  },
  {
    name: "Email Marketing Service",
    description:
      "Connect your email marketing service to automate email campaigns.",
    status: "Connected",
    account: "Tech Solutions Inc.",
    lastConnected: "July 20, 2025, 10:30 AM",
  },
];

function StatusBadge({ status }: { status: Status }) {
  return (
    <span
      style={{
        background: status === "Connected" ? "#cbfbe1" : "#fad1d1",
        color: status === "Connected" ? "#009c6f" : "#db3c3c",
        borderRadius: "9999px",
        padding: "6px 18px",
        fontWeight: 500,
        fontSize: "15px",
        display: "inline-block",
        minWidth: 102,
        textAlign: "center",
      }}
    >
      {status}
    </span>
  );
}

export default function IntegrationsPage() {
  const [search, setSearch] = useState("");
  const [modalOpen, setModalOpen] = useState<number | null>(null);

  const filtered = INTEGRATIONS.filter((i) =>
    `${i.name} ${i.description}`.toLowerCase().includes(search.toLowerCase()),
  );

  return (
    <div className="min-h-screen bg-gray-100 sm:py-8 py-2">
      <div className="max-w-full mx-8">
        <div className="text-2xl font-semibold text-gray-800 pt-2 pb-1">
          Integrations
        </div>
        <div className="text-gray-400 text-sm pb-4 pl-1">
          Dashboard / <span className="text-gray-800">Integrations</span>
        </div>
        <div className="rounded-2xl shadow bg-white px-4 sm:px-8 py-6">
          <div className="mb-4 flex items-center bg-gray-100 rounded-xl px-4 py-3">
            <span className="material-symbols-outlined text-gray-400 pr-2">
              search
            </span>
            <input
              aria-label="Search integrations"
              placeholder="Search integrations..."
              className="flex-1 bg-transparent border-0 outline-none text-base"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="overflow-x-auto">
            <table className="w-full border-separate border-spacing-0 text-base bg-white rounded-2xl">
              <thead>
                <tr className="text-gray-500 font-medium text-sm">
                  <th className="px-4 py-4 text-left">Name</th>
                  <th className="px-4 py-4 text-left">Description</th>
                  <th className="px-4 py-4 text-left">Status</th>
                  <th className="px-4 py-4 text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filtered.map((item, idx) => (
                  <tr
                    key={item.name}
                    className={`border-t last:border-b-0 border-gray-200 ${idx % 2 ? "bg-gray-50" : "bg-white"}`}
                  >
                    <td className="py-4 font-semibold text-gray-800 px-4">
                      {item.name}
                    </td>
                    <td className="py-4 text-gray-600 px-4">
                      {item.description}
                    </td>
                    <td className="py-4 px-4">
                      <StatusBadge status={item.status} />
                    </td>
                    <td className="py-4 px-4">
                      <button
                        className="text-[#214060] font-medium focus:outline-none underline decoration-solid underline-offset-2"
                        style={{ color: "#214060" }}
                        onClick={() => setModalOpen(idx)}
                      >
                        {item.status === "Connected" ? "Manage" : "Connect"}
                      </button>
                    </td>
                  </tr>
                ))}
                {!filtered.length && (
                  <tr>
                    <td colSpan={4} className="text-center py-8 text-gray-400">
                      No integrations found.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
        <ManageIntegrationModal
          open={modalOpen !== null}
          onClose={() => setModalOpen(null)}
          integration={modalOpen !== null ? filtered[modalOpen] : undefined}
        />
      </div>
    </div>
  );
}