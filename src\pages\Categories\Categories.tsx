import React, { useState } from "react";
import Breadcrumb from "../../components/Breadcrumb/Breadcrumb";
import AddCategoryModal from "./components/AddCategoryModal";

interface Category {
  id: number;
  icon: React.ReactNode;
  name: string;
  description: string;
  status: "Active" | "Inactive";
}

interface CategoryFormData {
  name: string;
  description: string;
  status: "Active" | "Inactive";
  iconId: string;
}

const availableIcons = [
  {
    id: "travel",
    component: <img src="planeIcon.svg" alt="Travel" className="w-6 h-6" />,
    name: "Travel",
  },
  {
    id: "meals",
    component: <img src="burgerCategoryIcon.svg" alt="Meals" className="w-6 h-6" />,
    name: "Meals",
  },
  {
    id: "office",
    component: <img src="penIcon.svg" alt="Office" className="w-6 h-6" />,
    name: "Office",
  },
  {
    id: "training",
    component: <img src="certificateIcon.svg" alt="Training" className="w-6 h-6" />,
    name: "Training",
  },
  {
    id: "misc",
    component: <img src="categoryIcon.svg" alt="Miscellaneous" className="w-6 h-6" />,
    name: "Miscellaneous",
  },
  {
    id: "lodging",
    component: <img src="lodgingIcon.svg" alt="Lodging" className="w-6 h-6" />,
    name: "Lodging",
  },
];

// Category List Component
const CategoryList: React.FC<{
  categories: Category[];
  onEdit: (id: number) => void;
}> = ({ categories, onEdit }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {["Icon", "Category Name", "Description", "Status", "Actions"].map((title) => (
                <th
                  key={title}
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  {title}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.map((category) => (
              <tr
                key={category.id}
                className="hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">{category.icon}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {category.name}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-500 max-w-xs">
                    {category.description}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {category.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => onEdit(category.id)}
                    className="text-blue-600 hover:text-blue-900 transition-colors"
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// AddCategoryModal component remains unchanged — use your original code.

// Main Component
export const Categories: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([
    {
      id: 1,
      icon: <img src="planeIcon.svg" alt="Travel" className="w-6 h-6" />,
      name: "Travel & Transportation",
      description:
        "Covers travel costs for business trips, including transportation, accommodation, and meals.",
      status: "Active",
    },
    {
      id: 2,
      icon: <img src="burgerCategoryIcon.svg" alt="Meals" className="w-6 h-6" />,
      name: "Meals & Entertainment",
      description:
        "Reimbursement for meals during business travel or client meetings.",
      status: "Active",
    },
    {
      id: 3,
      icon: <img src="penIcon.svg" alt="Office" className="w-6 h-6" />,
      name: "Office Supplies",
      description: "Covers the purchase of necessary office supplies.",
      status: "Active",
    },
    {
      id: 4,
      icon: <img src="certificateIcon.svg" alt="Training" className="w-6 h-6" />,
      name: "Training & Professional Services",
      description:
        "Reimbursement for professional development courses and training programs.",
      status: "Active",
    },
    {
      id: 5,
      icon: <img src="miscIcon.svg" alt="Misc" className="w-6 h-6" />,
      name: "Miscellaneous",
      description: "Petty cash, office refreshments, other ad-hoc expenses",
      status: "Active",
    },
    {
      id: 6,
      icon: <img src="lodgingIcon.svg" alt="Lodging" className="w-6 h-6" />,
      name: "Lodging",
      description: "Hotels, serviced apartments, conference accommodations",
      status: "Inactive",
    },
  ]);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddCategory = (data: CategoryFormData) => {
    const selectedIcon = availableIcons.find((icon) => icon.id === data.iconId);
    if (!selectedIcon) return;

    const newCategory: Category = {
      id: categories.length + 1,
      icon: selectedIcon.component,
      name: data.name,
      description: data.description,
      status: data.status,
    };

    setCategories((prev) => [...prev, newCategory]);
  };

  const handleEdit = (id: number) => {
    console.log("Edit category with ID:", id);
    // implement edit modal logic if required
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto shadow-2xl rounded-2xl shadow-amber-300">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div className="p-4">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-500">
              Categories
            </h1>
            <Breadcrumb pageName="Categories" />
          </div>
          <button
            onClick={() => setIsModalOpen(true)}
            className="mt-4 mx-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-slate-700 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors"
          >
            + Add Category
          </button>
        </div>

        <CategoryList categories={categories} onEdit={handleEdit} />

        <AddCategoryModal
          availableIcons={availableIcons}
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleAddCategory}
        />
      </div>
    </div>
  );
};

export default Categories;
