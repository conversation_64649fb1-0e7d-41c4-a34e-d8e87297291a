import React, { useState } from "react";
import { useForm } from "react-hook-form";
import Breadcrumb from '../../components/Breadcrumb/Breadcrumb';

interface Category {
  id: number;
  icon: React.ReactNode;
  name: string;
  description: string;
  status: "Active" | "Inactive";
}

interface CategoryFormData {
  name: string;
  description: string;
  status: "Active" | "Inactive";
  iconId: string;
}

const IconTravel = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    ✈️
  </div>
);

const IconMeals = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    🍽️
  </div>
);

const IconOffice = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    📝
  </div>
);

const IconTraining = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    🎓
  </div>
);

const IconMisc = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    📦
  </div>
);

const IconLodging = () => (
  <div className="w-8 h-8 bg-teal-600 rounded flex items-center justify-center text-white text-sm">
    🏨
  </div>
);

const availableIcons = [
  { id: "travel", component: <IconTravel />, name: "Travel" },
  { id: "meals", component: <IconMeals />, name: "Meals" },
  { id: "office", component: <IconOffice />, name: "Office" },
  { id: "training", component: <IconTraining />, name: "Training" },
  { id: "misc", component: <IconMisc />, name: "Miscellaneous" },
  { id: "lodging", component: <IconLodging />, name: "Lodging" },
];

// Category List Component
const CategoryList: React.FC<{
  categories: Category[];
  onEdit: (id: number) => void;
}> = ({ categories, onEdit }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Icon
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {categories.map((category) => (
              <tr
                key={category.id}
                className="hover:bg-gray-50 transition-colors"
              >
                <td className="px-6 py-4 whitespace-nowrap">{category.icon}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {category.name}
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-500 max-w-xs">
                    {category.description}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      category.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {category.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => onEdit(category.id)}
                    className="text-blue-600 hover:text-blue-900 transition-colors"
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Add Category Modal Component
// Fixed AddCategoryModal Component
const AddCategoryModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CategoryFormData) => void;
}> = ({ isOpen, onClose, onSubmit }) => {
  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CategoryFormData>({
    defaultValues: {
      status: "Active",
      iconId: "",
    },
  });

  const selectedIconId = watch("iconId");

  const handleFormSubmit = (data: CategoryFormData) => {
    onSubmit(data);
    reset();
    onClose();
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  // Handle escape key
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden"; // Prevent background scrolling
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, handleClose]);

  return (
    <div
      className={`fixed inset-0 z-50 overflow-y-auto transition-all duration-300 ${
        isOpen ? "opacity-100 visible" : "opacity-0 invisible"
      }`}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
      onClick={handleBackdropClick}
    >
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div
          className={`fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity duration-300 ${
            isOpen ? "opacity-100" : "opacity-0"
          }`}
          aria-hidden="true"
        ></div>

        <span
          className="hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
        >
          &#8203;
        </span>

        <div
          className={`inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all duration-300 sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full ${
            isOpen
              ? "translate-y-0 opacity-100 scale-100"
              : "translate-y-4 opacity-0 scale-95"
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="flex justify-between items-center mb-6">
              <h3
                className="text-lg font-semibold text-gray-900"
                id="modal-title"
              >
                Add New Category
              </h3>
              <button
                onClick={handleClose}
                className="text-red-600 hover:text-red-800 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 rounded px-2 py-1"
              >
                Close
              </button>
            </div>

            <form
              onSubmit={handleSubmit(handleFormSubmit)}
              className="space-y-6"
            >
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category Name
                  </label>
                  <input
                    type="text"
                    {...register("name", {
                      required: "Category name is required",
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter category name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Icons
                  </label>
                  <div className="grid grid-cols-4 gap-2 p-3 border border-gray-300 rounded-md min-h-[100px]">
                    {availableIcons.map((icon) => (
                      <button
                        key={icon.id}
                        type="button"
                        onClick={() => setValue("iconId", icon.id)}
                        className={`p-2 rounded-md border-2 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                          selectedIconId === icon.id
                            ? "border-blue-500 bg-blue-50"
                            : "border-gray-200 hover:border-gray-300"
                        }`}
                      >
                        {icon.component}
                      </button>
                    ))}
                  </div>
                  {!selectedIconId && (
                    <p className="mt-1 text-sm text-gray-500">
                      Please select an icon
                    </p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  {...register("description")}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  placeholder="Enter category description"
                />
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                <div className="text-gray-600 mb-2">
                  <svg
                    className="mx-auto h-12 w-12 text-gray-400"
                    stroke="currentColor"
                    fill="none"
                    viewBox="0 0 48 48"
                  >
                    <path
                      d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <p className="text-gray-600 mb-2">
                  Drag and drop an image here, or click to browse
                </p>
                <p className="text-sm text-gray-500">
                  SVG, PNG, JPG or GIF (max. 800×400px)
                </p>
                <button
                  type="button"
                  className="mt-3 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  Upload
                </button>
              </div>

              <div className="flex justify-end pt-4 border-t">
                <div className="flex space-x-3">
                  <button
                    type="button"
                    onClick={handleClose}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-slate-500 transition-colors disabled:opacity-50"
                    disabled={!selectedIconId}
                  >
                    Submit
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Category Management Component
export const Categories: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([
    {
      id: 1,
      icon: <IconTravel />,
      name: "Travel & Transportation",
      description:
        "Covers travel costs for business trips, including transportation, accommodation, and meals.",
      status: "Active",
    },
    {
      id: 2,
      icon: <IconMeals />,
      name: "Meals & Entertainment",
      description:
        "Reimbursement for meals during business travel or client meetings.",
      status: "Active",
    },
    {
      id: 3,
      icon: <IconOffice />,
      name: "Office Supplies",
      description: "Covers the purchase of necessary office supplies.",
      status: "Active",
    },
    {
      id: 4,
      icon: <IconTraining />,
      name: "Training & Professional Services",
      description:
        "Reimbursement for professional development courses and training programs.",
      status: "Active",
    },
    {
      id: 5,
      icon: <IconMisc />,
      name: "Miscellaneous",
      description: "Petty cash, office refreshments, other ad-hoc expenses",
      status: "Active",
    },
    {
      id: 6,
      icon: <IconLodging />,
      name: "Lodging",
      description: "Hotels, serviced apartments, conference accommodations",
      status: "Inactive",
    },
  ]);

  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleAddCategory = (data: CategoryFormData) => {
    const selectedIcon = availableIcons.find((icon) => icon.id === data.iconId);
    if (!selectedIcon) return;

    const newCategory: Category = {
      id: categories.length + 1,
      icon: selectedIcon.component,
      name: data.name,
      description: data.description,
      status: data.status,
    };

    setCategories((prev) => [...prev, newCategory]);
  };

  const handleEdit = (id: number) => {
    console.log("Edit category with ID:", id);
    // Implement edit functionality here
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8 ">
      <div className="max-w-7xl mx-auto shadow-2xl rounded-2xl shadow-amber-300">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
          <div className="p-4">
            <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
            <p className="text-sm text-gray-600 mt-1">Dashboard / Categories</p>
          </div>
          <button
            onClick={() => setIsModalOpen(true)}
            className="mt-4 mx-4 sm:mt-0 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-slate-700 hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors"
          >
            + Add Category
          </button>
        </div>

        <CategoryList categories={categories} onEdit={handleEdit} />

        <AddCategoryModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleAddCategory}
        />
      </div>
    </div>
  );
};

export default Categories;