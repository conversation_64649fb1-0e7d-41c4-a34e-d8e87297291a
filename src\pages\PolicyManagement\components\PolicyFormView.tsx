import React from 'react';
import { useForm } from 'react-hook-form';
import type {SubmitHandler} from 'react-hook-form'
import { ArrowLeft } from 'lucide-react';

type FormValues = {
  policyName: string;
  description: string;
  maxFrequency: string;
  receiptRequirement: string;
  expenseCategories: string;
  maxAmount: number;
  perDiem: number;
  destinationTypes: string;
  bookingClass: string;
  advanceBookingReq: string;
  applicableDepartments: string;
  applicableRoles: string;
};

type PolicyFormViewProps = {
  editingPolicy?: Partial<FormValues> | null;
  handleBackToList: () => void;
};

const PolicyFormView: React.FC<PolicyFormViewProps> = ({
  editingPolicy,
  handleBackToList,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormValues>({
    defaultValues: editingPolicy || {
      policyName: '',
      description: '',
      maxFrequency: '',
      receiptRequirement: '',
      expenseCategories: '',
      maxAmount: 0,
      perDiem: 0,
      destinationTypes: '',
      bookingClass: '',
      advanceBookingReq: '',
      applicableDepartments: '',
      applicableRoles: '',
    },
  });

  const onSubmit: SubmitHandler<FormValues> = (data) => {
    console.log('Form submitted:', data);
    alert('Policy saved successfully!');
    handleBackToList();
  };

  const onReset = () => reset();

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="bg-white rounded-xl shadow-lg p-8 w-full max-w-4xl mx-auto"
    >
      <div className="flex items-center gap-2 mb-6 cursor-pointer" onClick={handleBackToList}>
        <ArrowLeft className="h-5 w-5 text-gray-500" />
        <span className="text-sm text-gray-700">Back to Policies</span>
      </div>

      <h2 className="text-2xl font-bold mb-6">Add / Edit Policy</h2>

      <div className="grid grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          {['policyName', 'description', 'maxFrequency', 'receiptRequirement', 'expenseCategories'].map((field) => (
            <div key={field}>
              <label className="block text-sm font-medium text-gray-700 capitalize">{field.replace(/([A-Z])/g, ' $1')}</label>
              <input
                {...register(field as keyof FormValues, { required: field === 'policyName' ? 'Required' : false })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
              />
              {errors[field as keyof FormValues] && (
                <p className="text-red-500 text-sm">{(errors[field as keyof FormValues] as any)?.message}</p>
              )}
            </div>
          ))}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          {[
            { name: 'maxAmount', type: 'number' },
            { name: 'perDiem', type: 'number' },
            { name: 'destinationTypes', type: 'text' },
            { name: 'bookingClass', type: 'text' },
            { name: 'advanceBookingReq', type: 'text' },
            { name: 'applicableDepartments', type: 'text' },
            { name: 'applicableRoles', type: 'text' },
          ].map(({ name, type }) => (
            <div key={name}>
              <label className="block text-sm font-medium text-gray-700 capitalize">{name.replace(/([A-Z])/g, ' $1')}</label>
              <input
                type={type}
                {...register(name as keyof FormValues, { valueAsNumber: type === 'number' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
              />
            </div>
          ))}
        </div>
      </div>

      <div className="mt-8 flex gap-4">
        <button
          type="submit"
          className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg"
        >
          Save Policy
        </button>
        <button
          type="button"
          onClick={onReset}
          className="bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold py-2 px-6 rounded-lg"
        >
          Reset
        </button>
      </div>
    </form>
  );
};

export default PolicyFormView;
