import { Calendar } from "lucide-react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface DatePickerWithLabelProps {
  selected: Date | null;
  onChange: (date: Date | null) => void;
  placeholderText?: string;
  className?: string;
  dateFormat?: string;
}

const DatePickerWithLabel = ({
  selected,
  onChange,
  placeholderText = "Date",
  className = "",
  dateFormat = "dd/MM/yyyy",
}: DatePickerWithLabelProps) => {
  return (
    <div className="relative flex items-center gap-2 px-3 py-2 rounded-xl bg-gray-50 hover:bg-gray-100 transition shadow-sm w-fit">
      <Calendar className="w-4 h-4 text-gray-500" />
      <DatePicker
        selected={selected}
        onChange={onChange}
        placeholderText={placeholderText}
        className={`bg-transparent outline-none text-sm text-gray-700 w-full ${className}`}
        dateFormat={dateFormat}
      />
    </div>
  );
};

export default DatePickerWithLabel;
